"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";

function WithdrawAccount() {
  // use network
  const networkService = new NetworkService();

  const [withdrawAccounts, setWithdrawAccounts] = useState([]);
  const [loading, setLoading] = useState(true);

  // fetch withdraw accounts
  const fetchWithdrawAccounts = async () => {
    try {
      const res = await networkService.get(ApiPath.withdrawAccounts);
      if (res.status === "completed") {
        setWithdrawAccounts(res.data.data.accounts);
      }
    } finally {
      setLoading(false);
    }
  };

  console.log("withdrawAccounts", withdrawAccounts);

  useEffect(() => {
    fetchWithdrawAccounts();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Withdrawal Accounts
        </h2>
        <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          Add New Account
        </button>
      </div>

      {/* Withdrawal Accounts */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Withdrawal All Accounts
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Track all your withdrawal accounts and their details.
          </p>
        </div>
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-6 text-center text-gray-500">Loading...</div>
          ) : withdrawAccounts.length > 0 ? (
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    SL NO
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Currency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {withdrawAccounts.map((account, index) => (
                  <tr
                    key={account.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap flex items-center gap-2">
                      <img
                        src={account.method.icon}
                        alt={account.method.name}
                        className="w-6 h-6 rounded"
                      />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {account.method.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {account.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                        View
                      </button>
                      <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="p-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  No withdrawal accounts found.
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Your withdrawal accounts will appear here.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default WithdrawAccount;
