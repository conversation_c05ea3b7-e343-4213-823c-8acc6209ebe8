"use client";
import React, { useState } from "react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

function CreateWithdrawAccount() {
  const [selectedWallet, setSelectedWallet] = useState("");
  const [selectedMethod, setSelectedMethod] = useState("");

  // Sample wallet options (replace with actual data)
  const walletOptions = [
    { id: "main", name: "Main Wallet", balance: "1,250.00 USD" },
    { id: "savings", name: "Savings Wallet", balance: "5,800.50 USD" },
    { id: "business", name: "Business Wallet", balance: "12,340.75 USD" },
    { id: "crypto", name: "Crypto Wallet", balance: "0.5 BTC" },
  ];

  // Sample withdraw method options (replace with actual data)
  const withdrawMethods = [
    { 
      id: "bank", 
      name: "Bank Transfer", 
      icon: "🏦",
      description: "Transfer to your bank account"
    },
    { 
      id: "paypal", 
      name: "PayPal", 
      icon: "💳",
      description: "Transfer to your PayPal account"
    },
    { 
      id: "crypto", 
      name: "Cryptocurrency", 
      icon: "₿",
      description: "Transfer to crypto wallet"
    },
    { 
      id: "mobile", 
      name: "Mobile Money", 
      icon: "📱",
      description: "Transfer to mobile money account"
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      <div className="flex items-center gap-4">
        <Link
          href="/withdraw/withdraw-account"
          className="inline-flex items-center p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Add New Withdrawal Account
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Configure a new account for withdrawing funds
          </p>
        </div>
      </div>

      {/* Main Form */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Account Configuration
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Select your wallet and preferred withdrawal method.
          </p>
        </div>

        <div className="p-6 space-y-6">
          {/* Wallet Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Select Wallet <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {walletOptions.map((wallet) => (
                <div
                  key={wallet.id}
                  className={`relative rounded-lg border-2 cursor-pointer transition-all ${
                    selectedWallet === wallet.id
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                  }`}
                  onClick={() => setSelectedWallet(wallet.id)}
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                          {wallet.name}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Balance: {wallet.balance}
                        </p>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedWallet === wallet.id
                          ? "border-blue-500 bg-blue-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}>
                        {selectedWallet === wallet.id && (
                          <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Withdrawal Method Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Withdrawal Method <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {withdrawMethods.map((method) => (
                <div
                  key={method.id}
                  className={`relative rounded-lg border-2 cursor-pointer transition-all ${
                    selectedMethod === method.id
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                  }`}
                  onClick={() => setSelectedMethod(method.id)}
                >
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                            {method.name}
                          </h3>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {method.description}
                          </p>
                        </div>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedMethod === method.id
                          ? "border-blue-500 bg-blue-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}>
                        {selectedMethod === method.id && (
                          <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Link
              href="/withdraw/withdraw-account"
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Cancel
            </Link>
            <button
              type="button"
              disabled={!selectedWallet || !selectedMethod}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateWithdrawAccount;
