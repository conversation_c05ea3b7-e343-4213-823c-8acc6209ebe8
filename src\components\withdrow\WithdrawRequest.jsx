"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";

function WithdrawRequest() {
  // use network
  const networkService = new NetworkService();
  const [walletData, setWalletData] = useState([]);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [withdrawMethod, setWithdrawMethod] = useState([]);

  const [step, setStep] = useState(1);
  const [transferResponse, setTransferResponse] = useState(null);

  console.log("selectedWallet", selectedWallet);

  // Step Navigation
  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));
  const resetSteps = () => setStep(1);

  // find wallet id
  const handleSelectWallet = (e) => {
    const wallet = walletData.find((w) => String(w.id) === e.target.value);
    setSelectedWallet(wallet || null);
  };

  // validation with toast
  const handleValidated = () => {
    if (!selectedWallet) {
      toast.error("Please select a wallet");
      return false;
    }

    const amt = Number(withdrawAmount);
    if (!Number.isFinite(amt) || amt <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }
    return true;
  };

  // fetch wallet data
  const fetchWithdrawData = async () => {
    try {
      const res = await networkService.get(ApiPath.allWallets);
      if (res.status === "completed") {
        setWalletData(res.data.data.wallets);
      }
    } finally {
    }
  };
  // fetch withdraw methods
  const fetchWithdrawMethods = async () => {
    try {
      const res = await networkService.get(ApiPath.withdrawMethods);
      if (res.status === "completed") {
        setWithdrawMethod(res.data.data);
      }
    } finally {
    }
  };

  useEffect(() => {
    fetchWithdrawData();
    fetchWithdrawMethods();
  }, []);

  return (
    <>
      <div className="flex items-center justify-between mb-5">
        <h2 className="text-xl font-semibold text-gray-900">Withdraw Funds</h2>
      </div>
      <div className="grid grid-cols-12">
        <div className="col-span-12 lg:col-span-5 lg:col-start-4">
          {/* Step Indicators */}
          <div className="max-w-lg mx-auto mb-6">
            <div className="flex justify-between">
              {[1, 2, 3].map((s) => (
                <div key={s} className={`step-${s}`}>
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-8 h-8 flex justify-center items-center rounded-full 
                      ${
                        step === s
                          ? "bg-blue-600 text-white"
                          : step > s
                          ? "bg-blue-500 text-white"
                          : "bg-gray-200 text-gray-600"
                      }`}
                    >
                      {s}
                    </div>
                    <div className="text-sm mt-1">
                      {s === 1 ? "Amount" : s === 2 ? "Review" : "Success"}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Step 1 - Withdraw Form */}
          {step === 1 && (
            <div className="space-y-6">
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Withdrawal Request
                  </h3>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Request a withdrawal from your available balance.
                  </p>
                </div>
                <div className="p-6">
                  <form
                    className="space-y-4"
                    onSubmit={(e) => {
                      e.preventDefault();
                      const ok = handleValidated(); // validate first
                      if (!ok) return; // stop if invalid
                      nextStep();
                    }}
                  >
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Withdraw Account
                      </label>
                      <select
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                        value={selectedWallet?.id || ""}
                        onChange={handleSelectWallet}
                      >
                        <option value="">Select a wallet</option>
                        {walletData.map((w) => (
                          <option key={w.id} value={w.id ?? 0}>
                            {w.name} {w.code} {w.formatted_balance}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Amount
                      </label>
                      <input
                        type="number"
                        placeholder="0.00"
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                      />

                      {selectedWallet && (
                        <p className="text-sm text-red-600 mt-1">
                          Minimum: {selectedWallet.min_amount}{" "}
                          {selectedWallet.code} {" "}
                          Maximum: {selectedWallet.max_amount}{" "}
                          {selectedWallet.code}
                        </p>
                      )}
                    </div>
                    <button
                      type="submit"
                      className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                    >
                      Request Withdrawal
                    </button>
                  </form>
                </div>
              </div>
            </div>
          )}

          {/* Step 2 - Confirmation */}
          {step === 2 && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Confirm withdrawal
                </h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  Request a withdrawal from your available balance.
                </p>
              </div>
              <div className="p-6">
                <ul className="flex gap-4 flex-col mb-6 text-gray-700">
                  <li className="flex justify-between">
                    <span>Select Wallet</span>
                    <span>
                      {selectedWallet
                        ? `${selectedWallet.name} (${selectedWallet.code})`
                        : ""}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span>Amount</span>
                    <span>{withdrawAmount}</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Charge</span>
                    <span>2.50</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Total Amount</span>
                    <span className="text-blue-600 font-semibold">
                      {(withdrawAmount - 2.5).toFixed(2)}
                    </span>
                  </li>
                </ul>
                <div className="flex gap-3">
                  <button
                    onClick={prevStep}
                    className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
                  >
                    Back
                  </button>
                  <button
                    onClick={() => {
                      const ok = handleValidated();
                      if (!ok) return;
                      nextStep();
                      if (!selectedWallet) {
                        toast.error("Please select a wallet");
                        return;
                      }

                      const amt = Number(withdrawAmount);
                      if (!Number.isFinite(amt) || amt <= 0) {
                        toast.error("Please enter a valid amount");
                        return;
                      }
                      nextStep(); // go to Step 3
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
                  >
                    Confirm & Proceed
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 3 - Success */}
          {step === 3 && (
            <div className="mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
              <h3 className="text-3xl font-semibold text-green-600 mb-4">
                {transferResponse?.message || "Withdrawal Successful!"}
              </h3>
              <div className="grid grid-cols-12 gap-6 mb-5">
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Transaction ID</p>
                    <p>{transferResponse?.data?.transaction?.tnx}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Description</p>
                    <p>{transferResponse?.data?.transaction?.description}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Charge</p>
                    <p>{transferResponse?.data?.transaction?.charge}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Final Amount</p>
                    <p>{transferResponse?.data?.transaction?.final_amount}</p>
                  </div>
                </div>
              </div>
              <button
                onClick={() => {
                  resetSteps();
                  setSelectedWallet(null);
                  setWithdrawAmount("");
                  setTransferResponse(null);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Make Another Withdrawal
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default WithdrawRequest;
