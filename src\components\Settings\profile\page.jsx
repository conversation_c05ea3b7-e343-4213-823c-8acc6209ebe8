"use client";

import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

function ProfileLayout() {
  const networkService = new NetworkService();

  // Profile picture state
  const fileInputRef = useRef(null);
  const [profilePicture, setProfilePicture] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const MAX_SIZE_BYTES = 1 * 1024 * 1024; // 1MB

  // Profile data state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [gender, setGender] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [joiningDate, setJoiningDate] = useState("");
  const [country, setCountry] = useState("");
  const [city, setCity] = useState("");
  const [zip, setZip] = useState("");
  const [address, setAddress] = useState("");
  const [countries, setCountries] = useState([]);

  // Fetch countries
  const fetchCountriesData = async () => {
    try {
      const res = await networkService.get(ApiPath.getCountries);
      if (res.status === "completed") {
        setCountries(res.data.data);
      }
    } catch (err) {
      console.error(err);
    }
  };

  // Fetch profile data
  const fetchProfileData = async () => {
    try {
      const res = await networkService.get(ApiPath.getProfile);
      if (res.status === "completed") {
        const data = res.data.data;
        setFirstName(data.first_name || "");
        setLastName(data.last_name || "");
        setUsername(data.username || "");
        setEmail(data.email || "");
        setGender(data.gender || "");
        setDateOfBirth(data.date_of_birth || "");
        setPhoneNumber(data.phone || "");
        setCountry(data.country || "");
        setCity(data.city || "");
        setZip(data.zip_code || "");
        setAddress(data.address || "");

        // Format joining date
        if (data.created_at) {
          const date = new Date(data.created_at);
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, "0");
          const dd = String(date.getDate()).padStart(2, "0");
          setJoiningDate(`${yyyy}-${mm}-${dd}`);
        }

        // Set profile picture preview
        if (data.avatar_path) {
          setPreviewUrl(data.avatar_path);
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    fetchCountriesData();
    fetchProfileData();
  }, []);

  // Handle file input
  const handleButtonClick = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files && e.target.files[0];
    if (!file) return;

    // Type validation
    if (!file.type.startsWith("image/")) {
      alert("Please select an image file (jpg/png/gif).");
      e.target.value = null;
      return;
    }

    // Size validation
    if (file.size > MAX_SIZE_BYTES) {
      alert("Image is too large. Max 1MB allowed.");
      e.target.value = null;
      return;
    }

    setProfilePicture(file);
  };

  useEffect(() => {
    if (!profilePicture) return;

    const url = URL.createObjectURL(profilePicture);
    setPreviewUrl(url);

    return () => URL.revokeObjectURL(url);
  }, [profilePicture]);

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    await postProfileData();
  };

  // Post profile data
  const postProfileData = async () => {
    try {
      const formData = new FormData();
      formData.append("first_name", firstName);
      formData.append("last_name", lastName);
      formData.append("username", username);
      formData.append("gender", gender);
      formData.append("date_of_birth", dateOfBirth);
      formData.append("email", email);
      formData.append("phone", phoneNumber);
      formData.append("country", country);
      formData.append("city", city);
      formData.append("zip_code", zip);
      formData.append("address", address);

      // Append new profile picture if selected
      if (profilePicture) {
        formData.append("avatar", profilePicture);
      }

      const res = await networkService.postFormData(
        ApiPath.postProfile,
        formData
      );

      if (res.status === "completed") {
        toast.success(res.data.message);

        // Update previewUrl with uploaded image
        if (res.data.data?.avatar_path) {
          setPreviewUrl(res.data.data.avatar_path);
        }
      }
    } finally {
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Profile Information
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Update your personal information and account details.
            </p>
          </div>
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-12 gap-4">
                {/* First Name */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Demo"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Last Name */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Merchant"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Username */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Username
                  </label>
                  <input
                    type="text"
                    value={username}
                    disabled
                    readOnly
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Gender */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Gender
                  </label>
                  <select
                    value={gender}
                    onChange={(e) => setGender(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="others">Others</option>
                  </select>
                </div>

                {/* Email */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled
                    readOnly
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Date Of Birth */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Date Of Birth
                  </label>
                  <input
                    type="date"
                    value={dateOfBirth}
                    onChange={(e) => setDateOfBirth(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Phone */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="+****************"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Joining Date */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Joining Date
                  </label>
                  <input
                    type="date"
                    value={joiningDate}
                    disabled
                    readOnly
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Country */}
                <div className="col-span-12">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Country
                  </label>
                  <select
                    value={country}
                    onChange={(e) => setCountry(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    {countries.map((c) => (
                      <option key={c.code} value={c.name}>
                        {c.name} ({c.dial_code})
                      </option>
                    ))}
                  </select>
                </div>

                {/* City */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    City
                  </label>
                  <input
                    type="text"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    placeholder="Dhaka"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Zip */}
                <div className="col-span-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Zip
                  </label>
                  <input
                    type="text"
                    value={zip}
                    onChange={(e) => setZip(e.target.value)}
                    placeholder="1216"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Address */}
                <div className="col-span-12">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Address
                  </label>
                  <textarea
                    rows={3}
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Enter your business address"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Submit */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Profile Picture */}
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Profile Picture
            </h2>

            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                {previewUrl ? (
                  <img
                    src={previewUrl}
                    alt="Profile preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-2xl font-bold text-gray-600 dark:text-gray-300">
                    {username.slice(0, 2).toUpperCase()}
                  </span>
                )}
              </div>

              <div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <button
                  type="button"
                  onClick={handleButtonClick}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  Change Picture
                </button>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  JPG, GIF or PNG. 1MB max.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileLayout;
